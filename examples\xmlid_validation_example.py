"""
Example demonstrating XML ID validation functionality

This example shows how to:
1. Enable strict XML ID validation during XML loading
2. <PERSON><PERSON> missing XML ID errors
3. Configure validation behavior
"""

import asyncio
import os
import sys
from unittest.mock import AsyncMock, MagicMock

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from erp.data.exceptions import XMLIDNotFoundError
from erp.data.loader import DataLoader
from erp.data.xmlid_manager import XMLIDManager


async def example_strict_validation():
    """Example of strict XML ID validation"""
    print("=== Strict XML ID Validation Example ===")

    # Create mock database manager
    mock_db_manager = MagicMock()
    mock_db_manager.fetchval = AsyncMock()
    mock_db_manager.fetchrow = AsyncMock()
    mock_db_manager.execute = AsyncMock()

    # Create XML ID manager with strict validation
    xmlid_manager = XMLIDManager(mock_db_manager, strict_validation=True)

    # Mock XML ID lookup to simulate missing XML ID
    xmlid_manager.xmlid_sql.xmlid_lookup = AsyncMock(return_value=None)

    try:
        # This will raise an exception because the XML ID doesn't exist
        await xmlid_manager.resolve_xmlid_to_record_id(
            "missing.xmlid", context="example field reference"
        )
    except XMLIDNotFoundError as e:
        print(f"✓ Caught expected error: {e}")
        print(f"  XML ID: {e.xml_id}")
        print(f"  Context: {e.context}")

    print()


async def example_permissive_validation():
    """Example of permissive XML ID validation"""
    print("=== Permissive XML ID Validation Example ===")

    # Create mock database manager
    mock_db_manager = MagicMock()
    mock_db_manager.fetchval = AsyncMock()
    mock_db_manager.fetchrow = AsyncMock()
    mock_db_manager.execute = AsyncMock()

    # Create XML ID manager with permissive validation (default)
    xmlid_manager = XMLIDManager(mock_db_manager, strict_validation=False)

    # Mock XML ID lookup to simulate missing XML ID
    xmlid_manager.xmlid_sql.xmlid_lookup = AsyncMock(return_value=None)

    # This will return None instead of raising an exception
    result = await xmlid_manager.resolve_xmlid_to_record_id("missing.xmlid")
    print(f"✓ Missing XML ID returned: {result}")

    print()


async def example_data_loader_configuration():
    """Example of configuring data loader with strict validation"""
    print("=== Data Loader Configuration Example ===")

    # Create mock database manager
    mock_db_manager = MagicMock()
    mock_db_manager.fetchval = AsyncMock()
    mock_db_manager.fetchrow = AsyncMock()
    mock_db_manager.execute = AsyncMock()

    # Configuration with strict XML ID validation enabled
    strict_config = {
        "strict_xmlid_validation": True,
        "parse_mode": "strict",
        "continue_on_error": False,
    }

    # Configuration with permissive XML ID validation
    permissive_config = {
        "strict_xmlid_validation": False,
        "parse_mode": "permissive",
        "continue_on_error": True,
    }

    # Create data loaders with different configurations
    strict_loader = DataLoader(mock_db_manager, strict_config)
    permissive_loader = DataLoader(mock_db_manager, permissive_config)

    print(
        f"✓ Strict loader validation: {strict_loader.xmlid_manager._strict_validation}"
    )
    print(
        f"✓ Permissive loader validation: {permissive_loader.xmlid_manager._strict_validation}"
    )

    print()


async def example_xml_loading_with_validation():
    """Example of XML loading with validation"""
    print("=== XML Loading with Validation Example ===")

    # Sample XML with missing reference
    xml_content = """<?xml version="1.0" encoding="utf-8"?>
    <data>
        <record id="test_record" model="test.model">
            <field name="name">Test Record</field>
            <field name="parent_id" ref="missing.parent"/>
        </record>
    </data>"""

    # Create mock database manager
    mock_db_manager = MagicMock()
    mock_db_manager.fetchval = AsyncMock()
    mock_db_manager.fetchrow = AsyncMock()
    mock_db_manager.execute = AsyncMock()

    # Create data loader with strict validation
    config = {"strict_xmlid_validation": True, "parse_mode": "strict"}
    loader = DataLoader(mock_db_manager, config)

    print("✓ Created data loader with strict XML ID validation")
    print("✓ XML content contains reference to 'missing.parent'")
    print("✓ In strict mode, this would cause an error during loading")

    print()


async def example_dynamic_validation_control():
    """Example of dynamically controlling validation"""
    print("=== Dynamic Validation Control Example ===")

    # Create mock database manager
    mock_db_manager = MagicMock()
    mock_db_manager.fetchval = AsyncMock()
    mock_db_manager.fetchrow = AsyncMock()
    mock_db_manager.execute = AsyncMock()

    # Create XML ID manager with permissive validation initially
    xmlid_manager = XMLIDManager(mock_db_manager, strict_validation=False)
    print(
        f"✓ Initial validation mode: {'strict' if xmlid_manager._strict_validation else 'permissive'}"
    )

    # Enable strict validation dynamically
    xmlid_manager.enable_strict_validation(True)
    print(
        f"✓ After enabling strict mode: {'strict' if xmlid_manager._strict_validation else 'permissive'}"
    )

    # Disable strict validation
    xmlid_manager.enable_strict_validation(False)
    print(
        f"✓ After disabling strict mode: {'strict' if xmlid_manager._strict_validation else 'permissive'}"
    )

    print()


async def main():
    """Run all examples"""
    print("XML ID Validation Examples")
    print("=" * 50)
    print()

    await example_strict_validation()
    await example_permissive_validation()
    await example_data_loader_configuration()
    await example_xml_loading_with_validation()
    await example_dynamic_validation_control()

    print("All examples completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
