"""
Processor Manager for ERP system

Coordinates and manages all data processors, handling processor selection,
execution order, and result aggregation.
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional, Type

from ...database.connection.manager import DatabaseManager
from ...logging import get_logger
from .action_processor import ActionProcessor
from .base import BaseDataProcessor, ProcessorResult
from .menu_processor import MenuProcessor
from .record_processor import RecordProcessor
from .security_processor import SecurityProcessor
from .view_processor import ViewProcessor
from .workflow_processor import WorkflowProcessor


@dataclass
class ProcessingResult:
    """Overall processing result"""

    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    skipped_items: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    processor_results: Dict[str, ProcessorResult] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

    @property
    def success_rate(self) -> float:
        """Calculate overall success rate"""
        if self.processed_items == 0:
            return 0.0
        return (self.successful_items / self.processed_items) * 100


class ProcessorManager:
    """
    Manager for coordinating data processors

    Handles processor registration, item routing, execution order,
    and result aggregation.
    """

    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize processor manager

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.logger = get_logger(__name__)

        # Processor registry
        self.processors: Dict[str, BaseDataProcessor] = {}
        self.processor_classes: Dict[str, Type[BaseDataProcessor]] = {
            "security": SecurityProcessor,
            "view": ViewProcessor,
            "action": ActionProcessor,
            "menu": MenuProcessor,
            "record": RecordProcessor,
            "workflow": WorkflowProcessor,
        }

        # Processing configuration
        self.parallel_processing = False
        self.max_concurrent_processors = 3
        self.continue_on_error = True

        # Initialize default processors
        self._initialize_default_processors()

    def _initialize_default_processors(self):
        """Initialize default processors"""
        for name, processor_class in self.processor_classes.items():
            self.register_processor(name, processor_class(self.db_manager))

    def register_processor(self, name: str, processor: BaseDataProcessor):
        """Register a data processor"""
        self.processors[name] = processor
        self.logger.debug(f"Registered processor: {name}")

    def unregister_processor(self, name: str):
        """Unregister a data processor"""
        if name in self.processors:
            del self.processors[name]
            self.logger.debug(f"Unregistered processor: {name}")

    def get_processor(self, name: str) -> Optional[BaseDataProcessor]:
        """Get a processor by name"""
        return self.processors.get(name)

    def list_processors(self) -> List[str]:
        """List all registered processors"""
        return list(self.processors.keys())

    async def process_data(
        self, data: List[Dict[str, Any]], context: Optional[Dict[str, Any]] = None
    ) -> ProcessingResult:
        """
        Process data using appropriate processors

        Args:
            data: List of data items to process
            context: Processing context

        Returns:
            ProcessingResult with overall statistics and results
        """
        result = ProcessingResult(total_items=len(data), start_time=datetime.utcnow())

        try:
            self.logger.info(f"Starting data processing with {len(data)} items")

            # Set context for all processors
            if context:
                for processor in self.processors.values():
                    processor.set_context(context)

            # Group items by processor
            processor_groups = await self._group_items_by_processor(data)

            # Process groups in order
            if self.parallel_processing:
                await self._process_groups_parallel(processor_groups, result)
            else:
                await self._process_groups_sequential(processor_groups, result)

            # Calculate final statistics
            result.processed_items = sum(
                pr.processed_count for pr in result.processor_results.values()
            )
            result.successful_items = sum(
                pr.success_count for pr in result.processor_results.values()
            )
            result.failed_items = sum(
                pr.error_count for pr in result.processor_results.values()
            )
            result.skipped_items = result.total_items - result.processed_items

            # Aggregate errors and warnings
            for processor_result in result.processor_results.values():
                result.errors.extend(processor_result.errors)
                result.warnings.extend(processor_result.warnings)

            self.logger.info(
                f"Data processing completed: {result.successful_items}/{result.total_items} successful, "
                f"{result.failed_items} failed, {result.skipped_items} skipped"
            )

        except Exception as e:
            error_msg = f"Data processing failed: {e}"
            result.errors.append(error_msg)
            self.logger.error(error_msg)

        finally:
            result.end_time = datetime.utcnow()
            if result.start_time:
                result.duration = (result.end_time - result.start_time).total_seconds()

        return result

    async def _group_items_by_processor(
        self, data: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Group data items by appropriate processor"""
        processor_groups = {}
        unprocessed_items = []

        for item in data:
            processor_name = await self._find_processor_for_item(item)
            if processor_name:
                if processor_name not in processor_groups:
                    processor_groups[processor_name] = []
                processor_groups[processor_name].append(item)
            else:
                unprocessed_items.append(item)
                self.logger.warning(
                    f"No processor found for item: {item.get('model', 'unknown')}"
                )

        if unprocessed_items:
            self.logger.warning(
                f"{len(unprocessed_items)} items could not be processed"
            )

        return processor_groups

    async def _find_processor_for_item(self, item: Dict[str, Any]) -> Optional[str]:
        """Find the appropriate processor for an item"""
        # Get processors sorted by processing order
        sorted_processors = sorted(
            self.processors.items(), key=lambda x: x[1].get_processing_order()
        )

        # Find first processor that can handle the item
        for name, processor in sorted_processors:
            if processor.can_process(item):
                return name

        return None

    async def _process_groups_sequential(
        self,
        processor_groups: Dict[str, List[Dict[str, Any]]],
        result: ProcessingResult,
    ):
        """Process processor groups sequentially"""
        # Sort processors by processing order
        sorted_groups = sorted(
            processor_groups.items(),
            key=lambda x: self.processors[x[0]].get_processing_order(),
        )

        for processor_name, items in sorted_groups:
            processor = self.processors[processor_name]

            try:
                self.logger.info(f"Processing {len(items)} items with {processor_name}")
                processor_result = await processor.process(items)
                result.processor_results[processor_name] = processor_result

                if processor_result.error_count > 0 and not self.continue_on_error:
                    self.logger.error(
                        f"Stopping processing due to errors in {processor_name}"
                    )
                    break

            except Exception as e:
                error_msg = f"Processor {processor_name} failed: {e}"
                result.errors.append(error_msg)
                self.logger.error(error_msg)

                if not self.continue_on_error:
                    break

    async def _process_groups_parallel(
        self,
        processor_groups: Dict[str, List[Dict[str, Any]]],
        result: ProcessingResult,
    ):
        """Process processor groups in parallel (with ordering constraints)"""
        # Group processors by processing order
        order_groups = {}
        for processor_name, items in processor_groups.items():
            processor = self.processors[processor_name]
            order = processor.get_processing_order()

            if order not in order_groups:
                order_groups[order] = []
            order_groups[order].append((processor_name, items))

        # Process each order group sequentially, but items within group in parallel
        for order in sorted(order_groups.keys()):
            group_items = order_groups[order]

            # Create tasks for parallel processing within the group
            tasks = []
            for processor_name, items in group_items:
                processor = self.processors[processor_name]
                task = asyncio.create_task(
                    self._process_with_processor(processor, items, processor_name)
                )
                tasks.append(task)

            # Wait for all tasks in this order group to complete
            try:
                processor_results = await asyncio.gather(*tasks, return_exceptions=True)

                for i, processor_result in enumerate(processor_results):
                    processor_name = group_items[i][0]

                    if isinstance(processor_result, Exception):
                        error_msg = (
                            f"Processor {processor_name} failed: {processor_result}"
                        )
                        result.errors.append(error_msg)
                        self.logger.error(error_msg)
                    else:
                        result.processor_results[processor_name] = processor_result

                        if (
                            processor_result.error_count > 0
                            and not self.continue_on_error
                        ):
                            self.logger.error(
                                f"Stopping processing due to errors in {processor_name}"
                            )
                            return

            except Exception as e:
                error_msg = f"Parallel processing failed for order group {order}: {e}"
                result.errors.append(error_msg)
                self.logger.error(error_msg)

                if not self.continue_on_error:
                    break

    async def _process_with_processor(
        self,
        processor: BaseDataProcessor,
        items: List[Dict[str, Any]],
        processor_name: str,
    ) -> ProcessorResult:
        """Process items with a specific processor"""
        try:
            self.logger.info(f"Processing {len(items)} items with {processor_name}")
            return await processor.process(items)
        except Exception as e:
            self.logger.error(f"Processor {processor_name} failed: {e}")
            raise

    def set_parallel_processing(self, enabled: bool, max_concurrent: int = 3):
        """Configure parallel processing"""
        self.parallel_processing = enabled
        self.max_concurrent_processors = max_concurrent
        self.logger.info(f"Parallel processing {'enabled' if enabled else 'disabled'}")

    def set_error_handling(self, continue_on_error: bool):
        """Configure error handling behavior"""
        self.continue_on_error = continue_on_error
        self.logger.info(f"Continue on error: {continue_on_error}")

    def configure_xmlid_validation(self, strict_validation: bool):
        """Configure strict XML ID validation for all processors"""
        for processor in self.processors.values():
            if hasattr(processor, "xmlid_manager"):
                processor.xmlid_manager.enable_strict_validation(strict_validation)
        self.logger.info(
            f"Strict XML ID validation {'enabled' if strict_validation else 'disabled'}"
        )

    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics for all processors"""
        stats = {
            "processors": {},
            "total_processors": len(self.processors),
            "parallel_processing": self.parallel_processing,
            "continue_on_error": self.continue_on_error,
        }

        for name, processor in self.processors.items():
            stats["processors"][name] = {
                "name": processor.name,
                "supported_models": processor.get_supported_models(),
                "processing_order": processor.get_processing_order(),
                "status": (
                    processor.status.value
                    if hasattr(processor, "status")
                    else "unknown"
                ),
            }

        return stats
